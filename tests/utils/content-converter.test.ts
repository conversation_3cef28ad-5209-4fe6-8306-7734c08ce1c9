import { describe, it, expect } from 'vitest';
import { ContentConverter } from '../../src/utils/content-converter';

describe('ContentConverter', () => {
  describe('createGhostPostData', () => {
    it('should never set "Content is being updated" placeholder', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      // Test with empty content
      const emptyContentResult = await ContentConverter.createGhostPostData(frontMatter, '');
      expect(emptyContentResult.html).toBe('<p></p>');
      expect(emptyContentResult.html).not.toContain('Content is being updated');

      // Test with whitespace-only content
      const whitespaceResult = await ContentConverter.createGhostPostData(frontMatter, '   \n  \t  ');
      expect(whitespaceResult.html).toBe('<p></p>');
      expect(whitespaceResult.html).not.toContain('Content is being updated');

      // Test with actual content
      const contentResult = await ContentConverter.createGhostPostData(frontMatter, 'Real content here');
      expect(contentResult.html).toContain('Real content here');
      expect(contentResult.html).not.toContain('Content is being updated');
    });

    it('should throw error when markdown conversion fails but content exists', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      // Mock a scenario where markdown exists but HTML conversion fails
      // This is hard to test directly, but we can verify the error handling logic
      const markdownContent = 'Some content that should convert';

      const result = await ContentConverter.createGhostPostData(frontMatter, markdownContent);

      // Should not throw and should contain actual content
      expect(result.html).toBeTruthy();
      expect(result.html).not.toBe('<p></p>');
      expect(result.html).not.toContain('Content is being updated');
    });

    it('should preserve existing feature image on updates', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      const existingPost = {
        id: '1',
        feature_image: 'https://example.com/image.jpg',
        updated_at: '2024-01-01T10:00:00.000Z'
      };

      const result = await ContentConverter.createGhostPostData(
        frontMatter,
        'Content',
        { isUpdate: true, existingPost }
      );

      expect(result.feature_image).toBe('https://example.com/image.jpg');
    });

    it('should include sync timestamp when syncing TO Ghost', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post',
        synced_at: '2024-01-01T09:00:00.000Z'
      };

      const result = await ContentConverter.createGhostPostData(frontMatter, 'Content');

      // The sync timestamp should be preserved in the post data for reference
      expect(result.title).toBe('Test Post');
      expect(result.slug).toBe('test-post');
    });
  });

  describe('convertGhostPostToArticle', () => {
    it('should NOT include internal sync timestamps in frontmatter', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>Test content</p>',
        tags: [] as any[]
      };

      const result = await ContentConverter.convertGhostPostToArticle(ghostPost);

      // Internal sync timestamps should NOT be in frontmatter anymore
      expect(result).not.toContain('Synced At:');
      expect(result).not.toContain('Changed At:');
      expect(result).not.toContain('synced_at:');
      expect(result).not.toContain('changed_at:');
      expect(result).toContain('Test content');
    });

    it('should handle posts with lexical content', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>HTML content</p>',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'markdown',
                markdown: '# Markdown Content\n\nThis is markdown content.'
              }
            ]
          }
        }),
        tags: [] as any[]
      };

      const result = await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(result).toContain('# Markdown Content');
      expect(result).toContain('This is markdown content');
      expect(result).not.toContain('<p>HTML content</p>');
    });

    it('should fallback to HTML when lexical has no markdown', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>HTML content</p>',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ text: 'Some other content' }]
              }
            ]
          }
        }),
        tags: [] as any[]
      };

      const result = await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(result).toContain('HTML content');
    });

    it('should handle empty content gracefully', async () => {
      const ghostPost = {
        id: '1',
        title: 'Empty Post',
        slug: 'empty-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '',
        tags: [] as any[]
      };

      const result = await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(result).toContain('Title: "Empty Post"');
      expect(result).toContain('Slug: "empty-post"');
      expect(result).not.toContain('Content is being updated');
    });
  });

  describe('parseMarkdown', () => {
    it('should parse frontmatter and content correctly', async () => {
      const content = `---
title: Test Post
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
---

# Test Content

This is the content.`;

      const result = ContentConverter.parseMarkdown(content);

      expect(result.frontMatter.title).toBe('Test Post');
      expect(result.frontMatter.slug).toBe('test-post');
      expect(result.frontMatter.synced_at).toBe('2024-01-01T10:00:00.000Z');
      expect(result.markdownContent).toBe('# Test Content\n\nThis is the content.');
    });

    it('should throw error when no frontmatter found', async () => {
      const content = 'Just content without frontmatter';

      expect(() => ContentConverter.parseMarkdown(content)).toThrow('No front matter found in article');
    });
  });

  describe('markdownToHtml', () => {
    it('should convert markdown to HTML properly', async () => {
      const markdown = '# Heading\n\n**Bold text** and *italic text*\n\n```javascript\nconsole.log("code");\n```';

      const result = ContentConverter.markdownToHtml(markdown);

      expect(result).toContain('<h1>Heading</h1>');
      expect(result).toContain('<strong>Bold text</strong>');
      expect(result).toContain('<em>italic text</em>');
      expect(result).toContain('<pre><code class="language-javascript">');
      expect(result).toContain('console.log("code");');
    });

    it('should handle empty markdown', async () => {
      const result = ContentConverter.markdownToHtml('');
      expect(result).toBe('<p></p>');
    });

    it('should handle Obsidian callouts', async () => {
      const markdown = '> [!info] Important\n> This is important information';

      const result = ContentConverter.markdownToHtml(markdown);

      expect(result).toContain('kg-callout-card');
      expect(result).toContain('Important');
      expect(result).toContain('This is important information');
    });
  });
});
