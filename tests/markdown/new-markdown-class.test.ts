/**
 * Tests for the new Markdown class implementation
 * This will help us verify functionality as we port from the original lexical-parser
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Markdown, markdownToLexical, lexicalToMarkdown, roundTrip } from '../../src/markdown';

describe('New Markdown Class', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Basic Functionality', () => {
    it('should convert simple text to lexical', async () => {
      const markdown = 'Hello world';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('paragraph');
    });

    it('should convert simple lexical to markdown', async () => {
      const lexicalDoc = {
        root: {
          type: 'root' as const,
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Hello world',
              detail: 0,
              format: 0,
              mode: 'normal' as const,
              style: '',
              version: 1
            }],
            direction: 'ltr' as const,
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        },
        nodes: [] as any[]
      };

      const result = await parser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello world');
    });

    it('should handle empty content', async () => {
      const result = await parser.markdownToLexical('');

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(0);
    });
  });

  describe('Text Formatting', () => {
    it('should convert bold text', async () => {
      const markdown = '**bold text**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const paragraph = result.data?.root.children[0];
      expect(paragraph.type).toBe('paragraph');
      expect(paragraph.children).toHaveLength(1);

      const textNode = paragraph.children[0];
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold format bit
    });

    it('should convert italic text with asterisks', async () => {
      const markdown = '*italic text*';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic format bit
    });

    it('should convert italic text with underscores', async () => {
      const markdown = '_italic text_';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic format bit
    });
  });

  describe('Headings', () => {
    it('should convert headings correctly', async () => {
      const markdown = '# Heading 1\n## Heading 2\n### Heading 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(3);

      expect(result.data?.root.children[0].type).toBe('heading');
      expect(result.data?.root.children[0].tag).toBe('h1');

      expect(result.data?.root.children[1].type).toBe('heading');
      expect(result.data?.root.children[1].tag).toBe('h2');

      expect(result.data?.root.children[2].type).toBe('heading');
      expect(result.data?.root.children[2].tag).toBe('h3');
    });
  });

  describe('Lists', () => {
    it('should convert unordered lists', async () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const list = result.data?.root.children[0];
      expect(list.type).toBe('list');
      expect(list.listType).toBe('bullet');
      expect(list.children).toHaveLength(3);
    });

    it('should convert ordered lists', async () => {
      const markdown = '1. Item 1\n2. Item 2\n3. Item 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const list = result.data?.root.children[0];
      expect(list.type).toBe('list');
      expect(list.listType).toBe('number');
      expect(list.children).toHaveLength(3);
    });
  });

  describe('Round-trip Conversion', () => {
    it('should preserve content through round-trip conversion', async () => {
      const originalMarkdown = '# Title\n\nThis is **bold** and *italic* text.\n\n- List item 1\n- List item 2';

      // Markdown → Lexical
      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      // Lexical → Markdown
      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // The result should be semantically equivalent (may have minor formatting differences)
      expect(markdownResult.data).toContain('# Title');
      expect(markdownResult.data).toContain('**bold**');
      expect(markdownResult.data).toContain('*italic*');
      expect(markdownResult.data).toContain('- List item 1');
      expect(markdownResult.data).toContain('- List item 2');
    });
  });

  describe('Advanced Features', () => {
    it('should convert Ghost callouts', async () => {
      const markdown = '> [!note]\n> This is a note callout';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const quote = result.data?.root.children[0];
      expect(quote.type).toBe('quote');
      expect(quote.children.length).toBe(3); // Marker paragraph + linebreak + text

      // Check that the first child contains the callout marker
      const firstChild = quote.children[0];
      expect(firstChild.type).toBe('text');
      expect(firstChild.text).toBe('[!note]');

      // Check that the last child contains the callout content
      const contentText = quote.children[2];
      expect(contentText.type).toBe('text');
      expect(contentText.text).toBe('This is a note callout');
    });

    it('should convert Obsidian wikilinks', async () => {
      const markdown = '[[Target Page|Display Text]]';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const paragraph = result.data?.root.children[0];
      expect(paragraph.type).toBe('paragraph');
      expect(paragraph.children).toHaveLength(1);

      const link = paragraph.children[0];
      expect(link.type).toBe('link');
      expect(link.url).toBe('obsidian://Target Page');
    });

    it('should convert simple wikilinks without alias', async () => {
      const markdown = '[[Target Page]]';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const link = paragraph.children[0];
      expect(link.type).toBe('link');
      expect(link.url).toBe('obsidian://Target Page');
    });

    it('should handle inline math expressions', async () => {
      const markdown = 'This is $x^2 + y^2 = z^2$ an equation';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];

      // For now, just check that it parses successfully
      // Math expressions are complex and may need dedicated node types
      expect(paragraph.children.length).toBeGreaterThan(0);
      expect(paragraph.children[0].text).toContain('x^2');
    });

    it('should convert Obsidian tags', async () => {
      const markdown = 'This has a #tag in it';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      expect(paragraph.children.length).toBe(3); // text + tag + text

      // Check that the tag is converted to a link
      const tagNode = paragraph.children[1];
      expect(tagNode.type).toBe('link');
      expect(tagNode.url).toBe('obsidian://tag/tag');
    });
  });

  describe('Convenience Functions', () => {
    it('should work with standalone markdownToLexical function', async () => {
      const result = await markdownToLexical('Hello world');
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
    });

    it('should work with standalone lexicalToMarkdown function', async () => {
      const lexicalDoc = {
        root: {
          type: 'root' as const,
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Hello world',
              detail: 0,
              format: 0,
              mode: 'normal' as const,
              style: '',
              version: 1
            }],
            direction: 'ltr' as const,
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        },
        nodes: [] as any[]
      };

      const result = await lexicalToMarkdown(lexicalDoc);
      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello world');
    });

    it('should work with roundTrip function', async () => {
      const originalMarkdown = 'Hello **world**';
      const result = await roundTrip(originalMarkdown);

      expect(result.success).toBe(true);
      expect(result.data).toContain('Hello');
      expect(result.data).toContain('**world**');
    });
  });

  describe('Enhanced Error Handling', () => {
    it('should handle null input gracefully', async () => {
      const result = await parser.markdownToLexical(null as any);
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('NULL_INPUT');
      expect(result.error).toContain('null or undefined');
    });

    it('should handle undefined input gracefully', async () => {
      const result = await parser.markdownToLexical(undefined as any);
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('NULL_INPUT');
    });

    it('should handle non-string input gracefully', async () => {
      const result = await parser.markdownToLexical(123 as any);
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('INVALID_INPUT');
      expect(result.error).toContain('must be a string');
    });

    it('should validate lexical document structure', async () => {
      const invalidDoc = {
        root: {
          type: 'invalid',
          children: []
        }
      } as any;

      const result = await parser.lexicalToMarkdown(invalidDoc);
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('VALIDATION_FAILED');
    });

    it('should provide metadata for successful conversions', async () => {
      const result = await parser.markdownToLexical('Hello **world**');
      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.processingTime).toBeGreaterThanOrEqual(0);
      expect(result.metadata?.nodeCount).toBeGreaterThan(0);
      expect(result.metadata?.retryCount).toBe(0);
    });

    it('should handle input validation when enabled', async () => {
      const result = await parser.markdownToLexical('Normal text', { validateInput: true });
      expect(result.success).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(Array.isArray(result.warnings)).toBe(true);
    });
  });
});
