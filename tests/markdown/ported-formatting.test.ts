/**
 * Ported formatting tests from the original lexical-parser
 * These tests ensure comprehensive text formatting support in the new Markdown class
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Markdown } from '../../src/markdown';

describe('Ported Formatting Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Basic Text Formatting', () => {
    it('should handle bold text with **', async () => {
      const markdown = '**bold text**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle bold text with __', async () => {
      const markdown = '__bold text__';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle italic text with *', async () => {
      const markdown = '*italic text*';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle italic text with _', async () => {
      const markdown = '_italic text_';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle strikethrough text', async () => {
      const markdown = '~~strikethrough text~~';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('strikethrough text');
      expect(textNode.format & 4).toBe(4); // Strikethrough flag
    });

    it('should handle inline code', async () => {
      const markdown = '`code text`';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('code text');
      expect(textNode.format & 16).toBe(16); // Code flag
    });
  });

  describe('Combined Formatting', () => {
    it('should handle bold and italic combined (***)', async () => {
      const markdown = '***bold and italic***';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('bold and italic');
      expect(textNode.format & 1).toBe(1); // Bold flag
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle bold and italic combined (___)', async () => {
      const markdown = '___bold and italic___';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('bold and italic');
      expect(textNode.format & 1).toBe(1); // Bold flag
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle nested formatting', async () => {
      const markdown = 'Normal **bold *and italic* text** normal';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have multiple text nodes with different formatting
      const hasPlainText = paragraph.children.some((node: any) => node.format === 0);
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);

      expect(hasPlainText).toBe(true);
      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
    });

    it('should handle code within other formatting', async () => {
      const markdown = '**Bold with `code` inside**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have nodes with different formatting
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasCodeText = paragraph.children.some((node: any) => (node.format & 16) === 16);

      expect(hasBoldText).toBe(true);
      expect(hasCodeText).toBe(true);
    });
  });

  describe('Mixed Content Formatting', () => {
    it('should handle multiple formatting types in one paragraph', async () => {
      const markdown = 'This is **bold** and *italic* and `code` and ~~strikethrough~~ text.';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have multiple text nodes with different formatting
      const hasPlainText = paragraph.children.some((node: any) => node.format === 0);
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);
      const hasStrikethroughText = paragraph.children.some((node: any) => (node.format & 4) === 4);
      const hasCodeText = paragraph.children.some((node: any) => (node.format & 16) === 16);

      expect(hasPlainText).toBe(true);
      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
      expect(hasStrikethroughText).toBe(true);
      expect(hasCodeText).toBe(true);
    });

    it('should handle the exact formatting issue from user report', async () => {
      const markdown = 'This is a paragraph with **bold text** and an _italic using underscore_ text too and also *italic using star*. Some ~~strike-through~~ text here. Here\'s `inline code snippet`.';

      // Test markdown to lexical conversion
      const lexicalResult = await parser.markdownToLexical(markdown);
      expect(lexicalResult.success).toBe(true);

      // Let's examine the actual Lexical JSON structure
      console.log('Lexical JSON:', JSON.stringify(lexicalResult.data, null, 2));

      // Check that the lexical document has the correct formatting flags
      const paragraph = lexicalResult.data?.root.children[0] as any;
      expect(paragraph.type).toBe('paragraph');

      // Find text nodes with different formatting
      const textNodes = paragraph.children;
      const boldNode = textNodes.find((node: any) => node.format & 1); // Bold flag
      const italicNodes = textNodes.filter((node: any) => node.format & 2); // Italic flag
      const strikethroughNode = textNodes.find((node: any) => node.format & 4); // Strikethrough flag
      const codeNode = textNodes.find((node: any) => node.format & 16); // Code flag

      expect(boldNode).toBeDefined();
      expect(italicNodes.length).toBeGreaterThanOrEqual(2); // Should have both italic styles
      expect(strikethroughNode).toBeDefined();
      expect(codeNode).toBeDefined();

      // Test lexical to markdown roundtrip
      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      console.log('Roundtrip markdown:', markdownResult.data);
    });

    it('should handle formatting at word boundaries', async () => {
      const markdown = 'Start**bold**middle*italic*end';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);
    });

    it('should handle formatting with punctuation', async () => {
      const markdown = 'Hello, **world**! How are *you* today?';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);

      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty formatting', async () => {
      const markdown = '****';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully without crashing
    });

    it('should handle unmatched formatting markers', async () => {
      const markdown = 'This has **unmatched bold';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully, treating as literal text
    });

    it('should handle escaped formatting', async () => {
      const markdown = 'This has \\*escaped\\* asterisks';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.text).toContain('*escaped*');
    });

    it('should handle formatting across line breaks', async () => {
      const markdown = '**Bold text\nacross lines**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully
    });
  });
});
