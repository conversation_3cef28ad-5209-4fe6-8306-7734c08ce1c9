import { ContentConverter } from '../src/utils/content-converter';

describe('ContentConverter', () => {
  describe('markdownToHtml', () => {
    it('should not return empty content for valid markdown', async () => {
      const markdown = '# Test Title\n\nSome content here.';
      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toBeTruthy();
      expect(html.trim()).not.toBe('');
      expect(html).toContain('<h1>Test Title</h1>');
      expect(html).toContain('Some content here');
    });

    it('should handle empty markdown gracefully', async () => {
      const html = ContentConverter.markdownToHtml('');
      expect(html).toBe('<p></p>');
    });

    it('should convert unordered lists to HTML', async () => {
      const markdown = `- First item
- Second item
- Third item`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<ul>');
      expect(html).toContain('<li>First item</li>');
      expect(html).toContain('<li>Second item</li>');
      expect(html).toContain('<li>Third item</li>');
      expect(html).toContain('</ul>');
    });

    it('should convert ordered lists to HTML', async () => {
      const markdown = `1. First item
2. Second item
3. Third item`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<ol>');
      expect(html).toContain('<li>First item</li>');
      expect(html).toContain('<li>Second item</li>');
      expect(html).toContain('<li>Third item</li>');
      expect(html).toContain('</ol>');
    });

    it('should handle lists with inline formatting', async () => {
      const markdown = `- **Bold item**
- *Italic item*
- [Link item](https://example.com)
- \`Code item\``;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<li><strong>Bold item</strong></li>');
      expect(html).toContain('<li><em>Italic item</em></li>');
      expect(html).toContain('<li><a href="https://example.com">Link item</a></li>');
      expect(html).toContain('<li><code>Code item</code></li>');
    });

    it('should handle mixed content with lists', async () => {
      const markdown = `# Title

Some paragraph text.

- List item 1
- List item 2

Another paragraph.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<h1>Title</h1>');
      expect(html).toContain('<p>Some paragraph text.</p>');
      expect(html).toContain('<ul>');
      expect(html).toContain('<li>List item 1</li>');
      expect(html).toContain('<li>List item 2</li>');
      expect(html).toContain('</ul>');
      expect(html).toContain('<p>Another paragraph.</p>');
    });

    it('should preserve code blocks with language', async () => {
      const markdown = '```javascript\nfunction test() {}\n```';
      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('class="language-javascript"');
      expect(html).toContain('function test()');
    });

    it('should handle complex content without loss', async () => {
      const markdown = `# Main Title

## Subtitle

This has **bold** and *italic* text.

\`\`\`elixir
defmodule Test do
  def hello, do: IO.puts("Hello!")
end
\`\`\`

Also a [link](https://example.com).`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<h1>Main Title</h1>');
      expect(html).toContain('<h2>Subtitle</h2>');
      expect(html).toContain('<strong>bold</strong>');
      expect(html).toContain('<em>italic</em>');
      expect(html).toContain('class="language-elixir"');
      expect(html).toContain('defmodule Test');
      expect(html).toContain('<a href="https://example.com">link</a>');
    });

    it('should convert Obsidian note callouts to Ghost callout cards', async () => {
      const markdown = `> [!note]
> This is a note callout with some important information.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-card kg-callout-card');
      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('kg-callout-emoji');
      expect(html).toContain('📝');
      expect(html).toContain('kg-callout-text');
      expect(html).toContain('This is a note callout with some important information.');
    });

    it('should convert single-line callouts (title-only format)', async () => {
      const markdown = `> [!info] Here's a callout title`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('ℹ️');
      expect(html).toContain('Here\'s a callout title');
    });

    it('should convert callouts with title and body', async () => {
      const markdown = `> [!warning] Important Warning
> This is the body content of the warning.
> It can span multiple lines.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-yellow');
      expect(html).toContain('⚠️');
      expect(html).toContain('Important Warning This is the body content of the warning. It can span multiple lines.');
    });

    it('should handle the exact user format with complex content', async () => {
      const markdown = `> [!info] Here's a callout title > Here's a callout block.

I wonder if callouts will work?

> [!tip] Here's a tip!`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('ℹ️');
      expect(html).toContain('Here\'s a callout title > Here\'s a callout block.');
      expect(html).toContain('kg-callout-card-green');
      expect(html).toContain('💡');
      expect(html).toContain('Here\'s a tip!');
      expect(html).toContain('<p>I wonder if callouts will work?</p>');
      // Ensure callouts are NOT wrapped in <p> tags
      expect(html).not.toContain('<p>> [!info]');
      expect(html).not.toContain('<p>> [!tip]');
    });

    it('should convert Obsidian warning callouts to Ghost callout cards', async () => {
      const markdown = `> [!warning]
> This is a warning callout.
> It can span multiple lines.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-yellow');
      expect(html).toContain('⚠️');
      expect(html).toContain('This is a warning callout. It can span multiple lines.');
    });

    it('should convert Obsidian tip callouts to Ghost callout cards', async () => {
      const markdown = `> [!tip]
> Here's a helpful tip for you!`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-green');
      expect(html).toContain('💡');
      expect(html).toContain('Here\'s a helpful tip for you!');
    });

    it('should convert Obsidian danger callouts to Ghost callout cards', async () => {
      const markdown = `> [!danger]
> This is dangerous! Be careful.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-red');
      expect(html).toContain('❌');
      expect(html).toContain('This is dangerous! Be careful.');
    });

    it('should handle unknown callout types with default note styling', async () => {
      const markdown = `> [!custom]
> This is a custom callout type.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('📝');
      expect(html).toContain('This is a custom callout type.');
    });

    it('should handle multiple callouts in the same content', async () => {
      const markdown = `# Title

> [!note]
> First callout

Some regular text here.

> [!warning]
> Second callout

More text.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<h1>Title</h1>');
      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('First callout');
      expect(html).toContain('kg-callout-card-yellow');
      expect(html).toContain('Second callout');
      expect(html).toContain('Some regular text here');
      expect(html).toContain('More text');
    });

    it('should handle callouts mixed with other markdown elements', async () => {
      const markdown = `# Documentation

> [!info]
> This is important information about the **API**.

\`\`\`javascript
function example() {
  return "code";
}
\`\`\`

> [!tip]
> Check out this [link](https://example.com) for more details.`;

      const html = ContentConverter.markdownToHtml(markdown);

      expect(html).toContain('<h1>Documentation</h1>');
      expect(html).toContain('kg-callout-card-blue');
      expect(html).toContain('This is important information about the <strong>API</strong>.');
      expect(html).toContain('class="language-javascript"');
      expect(html).toContain('function example()');
      expect(html).toContain('kg-callout-card-green');
      expect(html).toContain('Check out this <a href="https://example.com">link</a> for more details.');
    });
  });

  describe('htmlToMarkdown', () => {
    it('should convert Ghost callout cards back to Obsidian callouts', async () => {
      const html = `<div class="kg-card kg-callout-card kg-callout-card-blue">
    <div class="kg-callout-emoji">📝</div>
    <div class="kg-callout-text">This is a note callout</div>
</div>`;

      const markdown = ContentConverter.htmlToMarkdown(html);

      expect(markdown).toContain('> [!note]');
      expect(markdown).toContain('> This is a note callout');
    });

    it('should convert warning callout cards back to Obsidian callouts', async () => {
      const html = `<div class="kg-card kg-callout-card kg-callout-card-yellow">
    <div class="kg-callout-emoji">⚠️</div>
    <div class="kg-callout-text">This is a warning</div>
</div>`;

      const markdown = ContentConverter.htmlToMarkdown(html);

      expect(markdown).toContain('> [!warning]');
      expect(markdown).toContain('> This is a warning');
    });

    it('should handle callout cards without emoji gracefully', async () => {
      const html = `<div class="kg-card kg-callout-card kg-callout-card-blue">
    <div class="kg-callout-text">This is a callout without emoji</div>
</div>`;

      const markdown = ContentConverter.htmlToMarkdown(html);

      expect(markdown).toContain('> [!note]'); // Should default to note
      expect(markdown).toContain('> This is a callout without emoji');
    });
  });

  describe('createGhostPostData', () => {
    const frontMatter = {
      title: 'Test Post',
      slug: 'test-post'
    };

    const markdownContent = '# Test\n\nSome content.';

    it('should create post data with HTML content', async () => {
      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent);

      expect(postData.title).toBe('Test Post');
      expect(postData.slug).toBe('test-post');
      expect(postData.html).toBeTruthy();
      expect(postData.html).toContain('<h1>Test</h1>');
      expect(postData.html).toContain('Some content');
    });

    it('should NOT clear lexical content when updating existing posts', async () => {
      const postData = await await ContentConverter.createGhostPostData(
        frontMatter,
        markdownContent,
        { isUpdate: true }
      );

      // CRITICAL: When updating, lexical should be undefined (not null) to preserve existing content
      expect(postData.lexical).toBeUndefined();
      expect(postData.mobiledoc).toBeNull();
      expect(postData.html).toBeTruthy();
    });

    it('should handle new posts correctly', async () => {
      const postData = await ContentConverter.createGhostPostData(
        frontMatter,
        markdownContent,
        { isUpdate: false }
      );

      // For new posts, we can set lexical to null since there's no existing content
      expect(postData.html).toBeTruthy();
      expect(postData.mobiledoc).toBeNull();
    });

    it('should never create empty HTML content', async () => {
      const emptyMarkdown = '';
      const postData = await ContentConverter.createGhostPostData(frontMatter, emptyMarkdown);

      // Even with empty markdown, should have some HTML structure
      expect(postData.html).toBeTruthy();
      expect(postData.html.trim()).not.toBe('');
    });

    it('should preserve all frontmatter properties and generate excerpt from content', async () => {
      const richFrontMatter = {
        title: 'Rich Post',
        slug: 'rich-post',
        status: 'published',
        featured: true,
        feature_image: 'https://example.com/image.jpg'
      };

      const postData = await await ContentConverter.createGhostPostData(richFrontMatter, markdownContent);

      expect(postData.title).toBe('Rich Post');
      expect(postData.slug).toBe('rich-post');
      expect(postData.status).toBe('published');
      expect(postData.featured).toBe(true);
      expect(postData.feature_image).toBe('https://example.com/image.jpg');
      // Excerpt should be generated from content, not frontmatter
      expect(postData.custom_excerpt).toContain('Some content');
    });

    it('should handle visibility property correctly', async () => {
      const frontMatterWithVisibility = {
        title: 'Members Only Post',
        slug: 'members-only',
        visibility: 'members'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatterWithVisibility, markdownContent);

      expect(postData.visibility).toBe('members');
    });

    it('should default visibility to public when not specified', async () => {
      const frontMatterWithoutVisibility = {
        title: 'Public Post',
        slug: 'public-post'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatterWithoutVisibility, markdownContent);

      expect(postData.visibility).toBe('public');
    });

    it('should handle tags with primary tag correctly', async () => {
      const frontMatterWithPrimaryTag = {
        title: 'Tagged Post',
        slug: 'tagged-post',
        tags: ['javascript', 'web', 'tutorial'],
        primary_tag: 'tutorial'
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithPrimaryTag, markdownContent);

      expect(postData.tags).toEqual(['tutorial', 'javascript', 'web']);
    });

    it('should handle primary tag when not in existing tags', async () => {
      const frontMatterWithNewPrimaryTag = {
        title: 'Tagged Post',
        slug: 'tagged-post',
        tags: ['javascript', 'web'],
        primary_tag: 'featured'
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithNewPrimaryTag, markdownContent);

      expect(postData.tags).toEqual(['featured', 'javascript', 'web']);
    });

    it('should preserve existing feature image when updating without frontmatter feature image', async () => {
      const frontMatterWithoutImage = {
        title: 'Test Post',
        slug: 'test-post'
      };
      const existingPost = {
        id: 'existing-id',
        feature_image: 'https://example.com/existing-image.jpg',
        updated_at: '2023-01-01T00:00:00.000Z'
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithoutImage, markdownContent, {
        isUpdate: true,
        existingPost: existingPost
      });

      expect(postData.feature_image).toBe('https://example.com/existing-image.jpg');
    });

    it('should override feature image when specified in frontmatter', async () => {
      const frontMatterWithImage = {
        title: 'Test Post',
        slug: 'test-post',
        feature_image: 'https://example.com/new-image.jpg'
      };
      const existingPost = {
        id: 'existing-id',
        feature_image: 'https://example.com/existing-image.jpg',
        updated_at: '2023-01-01T00:00:00.000Z'
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithImage, markdownContent, {
        isUpdate: true,
        existingPost: existingPost
      });

      expect(postData.feature_image).toBe('https://example.com/new-image.jpg');
    });

    it('should set feature image to null for new posts without frontmatter feature image', async () => {
      const frontMatterWithoutImage = {
        title: 'Test Post',
        slug: 'test-post'
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithoutImage, markdownContent, {
        isUpdate: false
      });

      expect(postData.feature_image).toBe(null);
    });

    it('should handle tags without primary tag', async () => {
      const frontMatterWithoutPrimaryTag = {
        title: 'Tagged Post',
        slug: 'tagged-post',
        tags: ['javascript', 'web', 'tutorial']
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithoutPrimaryTag, markdownContent);

      expect(postData.tags).toEqual(['javascript', 'web', 'tutorial']);
    });

    it('should always generate excerpts from content, ignoring frontmatter', async () => {
      const frontMatterWithExcerpt = {
        title: 'Test Post',
        slug: 'test-post',
        excerpt: 'This excerpt should be ignored'
      };

      const contentWithText = '# Title\n\nThis is the actual content that should be used for excerpt generation.';
      const postData = await await ContentConverter.createGhostPostData(frontMatterWithExcerpt, contentWithText);

      // Should generate excerpt from content, not use frontmatter excerpt
      expect(postData.custom_excerpt).toContain('This is the actual content');
      expect(postData.custom_excerpt).not.toContain('This excerpt should be ignored');
    });
  });

  describe('Published Date Handling', () => {
    const frontMatter = { title: 'Test Post', slug: 'test-post' };
    const markdownContent = '# Test\n\nContent here.';

    it('should preserve existing published_at when updating published posts', async () => {
      const existingPublishedDate = '2023-01-15T10:00:00.000Z';
      const existingPost = {
        status: 'published',
        published_at: existingPublishedDate as string | null
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'published',
        isUpdate: true,
        existingPost: existingPost
      });

      // Should preserve the existing published_at date
      expect(postData.published_at).toBe(existingPublishedDate);
    });

    it('should set published_at when transitioning from draft to published', async () => {
      const existingPost = {
        status: 'draft',
        published_at: null as string | null
      };

      const beforeTime = new Date();
      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'published',
        isUpdate: true,
        existingPost: existingPost
      });
      const afterTime = new Date();

      // Should set a new published_at date
      expect(postData.published_at).toBeDefined();
      expect(postData.published_at).not.toBeNull();

      const publishedDate = new Date(postData.published_at);
      expect(publishedDate.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
      expect(publishedDate.getTime()).toBeLessThanOrEqual(afterTime.getTime());
    });

    it('should not set published_at when updating draft posts', async () => {
      const existingPost = {
        status: 'draft',
        published_at: null as string | null
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'draft',
        isUpdate: true,
        existingPost: existingPost
      });

      // Should not set published_at for draft posts
      expect(postData.published_at).toBeUndefined();
    });

    it('should set published_at for new published posts', async () => {
      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'published',
        isUpdate: false
      });

      // Should set published_at for new published posts
      expect(postData.published_at).toBeDefined();
      expect(postData.published_at).not.toBeNull();
    });

    it('should not set published_at for new draft posts', async () => {
      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'draft',
        isUpdate: false
      });

      // Should not set published_at for new draft posts
      expect(postData.published_at).toBeUndefined();
    });

    it('should use frontmatter published_at date for new published posts', async () => {
      const customDate = '2023-06-15T14:30:00.000Z';
      const frontMatterWithDate = {
        title: 'Test Post',
        slug: 'test-post',
        'Published At': customDate
      };

      const postData = await await ContentConverter.createGhostPostData(frontMatterWithDate, markdownContent, {
        status: 'published',
        isUpdate: false
      });

      // Should use the frontmatter date
      expect(postData.published_at).toBe(customDate);
    });

    it('should handle edge case of published post without published_at', async () => {
      const existingPost = {
        status: 'published',
        published_at: null as string | null // Edge case: published but no date
      };

      const beforeTime = new Date();
      const postData = await await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'published',
        isUpdate: true,
        existingPost: existingPost
      });
      const afterTime = new Date();

      // Should set a published_at date for this edge case
      expect(postData.published_at).toBeDefined();
      expect(postData.published_at).not.toBeNull();

      const publishedDate = new Date(postData.published_at);
      expect(publishedDate.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
      expect(publishedDate.getTime()).toBeLessThanOrEqual(afterTime.getTime());
    });
  });

  describe('Ghost to Obsidian Conversion', () => {
    it('should convert Ghost post with visibility and primary tag', async () => {
      const ghostPost: any = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'published',
        visibility: 'members',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z',
        tags: [
          { name: 'featured' },
          { name: 'javascript' },
          { name: 'tutorial' }
        ],
        primary_tag: { name: 'featured' },
        html: '<p>Test content</p>',
        lexical: null
      };

      const article = await await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(article).toContain('Title: "Test Post"');
      expect(article).toContain('Visibility: "members"');
      expect(article).not.toContain('Primary Tag:'); // Primary Tag should NOT be in frontmatter
      expect(article).toContain('Tags:\n  - featured\n  - javascript\n  - tutorial');
    });

    it('should handle Ghost post with public visibility and no primary tag', async () => {
      const ghostPost: any = {
        title: 'Public Post',
        slug: 'public-post',
        status: 'draft',
        visibility: 'public',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        tags: [
          { name: 'general' }
        ],
        primary_tag: null,
        html: '<p>Public content</p>',
        lexical: null
      };

      const article = await await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(article).toContain('Title: "Public Post"');
      expect(article).toContain('Visibility: "public"');
      expect(article).not.toContain('Primary Tag:'); // Primary Tag should NOT be in frontmatter
      expect(article).toContain('Tags:\n  - general');
    });

    it('should handle Ghost post with no tags', async () => {
      const ghostPost: any = {
        title: 'No Tags Post',
        slug: 'no-tags-post',
        status: 'draft',
        visibility: 'public',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        tags: [],
        primary_tag: null,
        html: '<p>Content without tags</p>',
        lexical: null
      };

      const article = await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(article).toContain('Title: "No Tags Post"');
      expect(article).toContain('Visibility: "public"');
      expect(article).not.toContain('Primary Tag:'); // Primary Tag should not be in frontmatter
      expect(article).toContain('Tags:');
    });
  });

  describe('Content Loss Prevention', () => {
    it('should never return null or undefined HTML', async () => {
      const testCases = [
        '',
        '   ',
        '\n\n',
        '# Title',
        '# Title\n\nContent',
        'Just plain text',
        '```\ncode\n```'
      ];

      testCases.forEach(markdown => {
        const html = ContentConverter.markdownToHtml(markdown);
        expect(html).toBeDefined();
        expect(html).not.toBeNull();
        expect(typeof html).toBe('string');
      });
    });

    it('should preserve content structure in complex scenarios', async () => {
      const complexMarkdown = `# API Documentation

## Authentication

Use the \`Authorization\` header:

\`\`\`bash
curl -H "Authorization: Bearer token" https://api.example.com
\`\`\`

### Response Format

The API returns **JSON** with the following structure:

\`\`\`json
{
  "status": "success",
  "data": {...}
}
\`\`\`

For more info, see [the docs](https://docs.example.com).`;

      const html = ContentConverter.markdownToHtml(complexMarkdown);

      // Verify all content is preserved
      expect(html).toContain('API Documentation');
      expect(html).toContain('Authentication');
      expect(html).toContain('Authorization');
      expect(html).toContain('Bearer token');
      expect(html).toContain('class="language-bash"');
      expect(html).toContain('class="language-json"');
      expect(html).toContain('"status": "success"');
      expect(html).toContain('<strong>JSON</strong>');
      expect(html).toContain('<a href="https://docs.example.com">the docs</a>');
    });
  });

  describe('normalizeFrontMatter', () => {
    it('should map title-cased properties to lowercase', async () => {
      const frontMatter = {
        'Title': 'Test Post',
        'Slug': 'test-post',
        'Status': 'published',
        'Tags': ['tag1', 'tag2']
      };

      const normalized = ContentConverter.normalizeFrontMatter(frontMatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.slug).toBe('test-post');
      expect(normalized.status).toBe('published');
      expect(normalized.tags).toEqual(['tag1', 'tag2']);
    });

    it('should handle visibility and primary tag properties', async () => {
      const frontMatter = {
        'Title': 'Test Post',
        'Visibility': 'members',
        'Primary Tag': 'featured',
        'Tags': ['featured', 'javascript']
      };

      const normalized = ContentConverter.normalizeFrontMatter(frontMatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.visibility).toBe('members');
      expect(normalized.primary_tag).toBe('featured');
      expect(normalized.tags).toEqual(['featured', 'javascript']);
    });

    it('should handle mixed case and preserve unknown properties', async () => {
      const frontMatter = {
        'Title': 'Test Post',
        'visibility': 'public', // lowercase
        'Primary Tag': 'tutorial', // title case
        'custom_field': 'value' // unknown property
      };

      const normalized = ContentConverter.normalizeFrontMatter(frontMatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.visibility).toBe('public');
      expect(normalized.primary_tag).toBe('tutorial');
      expect(normalized.custom_field).toBe('value');
    });
  });

  describe('Tag Sync Consistency', () => {
    it('should maintain tag order consistency between Ghost and Obsidian', async () => {
      // Simulate a Ghost post with specific tag order
      const ghostPost: any = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'published',
        visibility: 'public',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        published_at: '2023-01-01T00:00:00.000Z',
        tags: [
          { name: 'primary-tag' },
          { name: 'secondary-tag' },
          { name: 'third-tag' }
        ],
        primary_tag: { name: 'primary-tag' },
        html: '<p>Test content</p>',
        lexical: null
      };

      // Convert Ghost post to article
      const article = await ContentConverter.convertGhostPostToArticle(ghostPost);

      // Parse the article back to get frontmatter
      const { frontMatter } = ContentConverter.parseArticle(article);
      const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);

      // Tags should be in the same order as Ghost
      expect(normalizedFrontMatter.tags).toEqual(['primary-tag', 'secondary-tag', 'third-tag']);

      // Now simulate creating Ghost post data from this frontmatter
      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');

      // Tags should still be in the same order
      expect(postData.tags).toEqual(['primary-tag', 'secondary-tag', 'third-tag']);
    });

    it('should handle primary tag reordering correctly', async () => {
      // Frontmatter with primary tag not first
      const frontMatter = {
        'Title': 'Test Post',
        'Tags': ['javascript', 'tutorial', 'web'],
        'Primary Tag': 'tutorial'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');

      // Primary tag should be moved to first position
      expect(postData.tags).toEqual(['tutorial', 'javascript', 'web']);
    });
  });

  describe('Primary Tag Validation and Edge Cases', () => {
    it('should handle null primary tag', async () => {
      const frontMatter: any = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: null
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['javascript', 'web']);
    });

    it('should handle undefined primary tag', async () => {
      const frontMatter: any = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: undefined
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['javascript', 'web']);
    });

    it('should handle empty string primary tag', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: ''
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['javascript', 'web']);
    });

    it('should handle whitespace-only primary tag', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: '   \t\n  '
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['javascript', 'web']);
    });

    it('should trim whitespace from primary tag', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: '  tutorial  '
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['tutorial', 'javascript', 'web']);
    });

    it('should handle non-string primary tag', async () => {
      const frontMatter: any = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: 123
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['javascript', 'web']);
    });

    it('should handle primary tag when tags array is empty', async () => {
      const frontMatter: any = {
        title: 'Test Post',
        tags: [],
        primary_tag: 'featured'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['featured']);
    });

    it('should handle primary tag when no tags array exists', async () => {
      const frontMatter = {
        title: 'Test Post',
        primary_tag: 'featured'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['featured']);
    });

    it('should handle case-insensitive primary tag removal', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['JavaScript', 'TUTORIAL', 'web'],
        primary_tag: 'tutorial'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['tutorial', 'JavaScript', 'web']);
    });

    it('should remove multiple instances of primary tag', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['tutorial', 'javascript', 'tutorial', 'web', 'Tutorial'],
        primary_tag: 'tutorial'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['tutorial', 'javascript', 'web']);
    });

    it('should handle very long primary tag by truncating', async () => {
      const longTag = 'a'.repeat(200); // Exceeds 191 character limit
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript'],
        primary_tag: longTag
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags[0]).toHaveLength(191);
      expect(postData.tags[0]).toBe('a'.repeat(191));
      expect(postData.tags).toEqual(['a'.repeat(191), 'javascript']);
    });

    it('should handle primary tag with special characters', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: 'C++ Programming'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['C++ Programming', 'javascript', 'web']);
    });

    it('should handle primary tag with Unicode characters', async () => {
      const frontMatter = {
        title: 'Test Post',
        tags: ['javascript', 'web'],
        primary_tag: '编程教程'
      };

      const postData = await ContentConverter.createGhostPostData(frontMatter, 'Test content');
      expect(postData.tags).toEqual(['编程教程', 'javascript', 'web']);
    });
  });

  describe('Primary Tag Validation Function', () => {
    it('should validate normal string', async () => {
      const result = ContentConverter.validateAndNormalizePrimaryTag('tutorial');
      expect(result).toBe('tutorial');
    });

    it('should trim whitespace', async () => {
      const result = ContentConverter.validateAndNormalizePrimaryTag('  tutorial  ');
      expect(result).toBe('tutorial');
    });

    it('should return null for empty string', async () => {
      const result = ContentConverter.validateAndNormalizePrimaryTag('');
      expect(result).toBeNull();
    });

    it('should return null for whitespace-only string', async () => {
      const result = ContentConverter.validateAndNormalizePrimaryTag('   \t\n  ');
      expect(result).toBeNull();
    });

    it('should return null for non-string values', async () => {
      expect(ContentConverter.validateAndNormalizePrimaryTag(null)).toBeNull();
      expect(ContentConverter.validateAndNormalizePrimaryTag(undefined)).toBeNull();
      expect(ContentConverter.validateAndNormalizePrimaryTag(123)).toBeNull();
      expect(ContentConverter.validateAndNormalizePrimaryTag({})).toBeNull();
      expect(ContentConverter.validateAndNormalizePrimaryTag([])).toBeNull();
    });

    it('should truncate very long strings', async () => {
      const longTag = 'a'.repeat(200);
      const result = ContentConverter.validateAndNormalizePrimaryTag(longTag);
      expect(result).toHaveLength(191);
      expect(result).toBe('a'.repeat(191));
    });

    it('should handle long string with trailing whitespace', async () => {
      const longTag = 'a'.repeat(190) + '   ';
      const result = ContentConverter.validateAndNormalizePrimaryTag(longTag);
      expect(result).toBe('a'.repeat(190));
    });
  });
});
